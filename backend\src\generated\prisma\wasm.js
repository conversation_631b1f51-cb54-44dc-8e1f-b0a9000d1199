
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/wasm-engine-edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  passwordHash: 'passwordHash',
  role: 'role',
  status: 'status',
  nickName: 'nickName',
  wechat: 'wechat',
  avatar: 'avatar',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductVersionScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  version: 'version',
  versionName: 'versionName',
  description: 'description',
  configTemplate: 'configTemplate',
  encryptionKey: 'encryptionKey',
  defaultPrice: 'defaultPrice',
  downloadLink: 'downloadLink',
  coverUrl: 'coverUrl',
  changelog: 'changelog',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuthorizationScalarFieldEnum = {
  id: 'id',
  distributorId: 'distributorId',
  versionId: 'versionId',
  customPrice: 'customPrice',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LicenseScalarFieldEnum = {
  id: 'id',
  versionId: 'versionId',
  licenseKey: 'licenseKey',
  status: 'status',
  verifyConfig: 'verifyConfig',
  activatedAt: 'activatedAt',
  distributorId: 'distributorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  DISTRIBUTOR: 'DISTRIBUTOR'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.ProductStatus = exports.$Enums.ProductStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.AuthorizationStatus = exports.$Enums.AuthorizationStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.LicenseStatus = exports.$Enums.LicenseStatus = {
  INACTIVE: 'INACTIVE',
  ACTIVE: 'ACTIVE',
  REVOKED: 'REVOKED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Product: 'Product',
  ProductVersion: 'ProductVersion',
  Authorization: 'Authorization',
  License: 'License'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\code\\verify\\backend\\src\\generated\\prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [
      "driverAdapters"
    ],
    "sourceFilePath": "C:\\Users\\<USER>\\code\\verify\\backend\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "6.13.0",
  "engineVersion": "361e86d0ea4987e9f53a565309b3eed797a6bcbd",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "sqlite",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\ngenerator client {\n  provider        = \"prisma-client-js\"\n  output          = \"../src/generated/prisma\"\n  previewFeatures = [\"driverAdapters\"]\n}\n\ndatasource db {\n  provider = \"sqlite\"\n  url      = env(\"DATABASE_URL\")\n}\n\n// 用户表\nmodel User {\n  id           Int        @id @default(autoincrement())\n  username     String     @unique\n  passwordHash String\n  role         UserRole\n  status       UserStatus @default(ACTIVE)\n\n  // 分发商基本信息\n  nickName String?\n  wechat   String?\n  avatar   String?\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // 关系\n  licenses       License[]\n  authorizations Authorization[]\n\n  @@index([role])\n  @@index([status])\n  @@index([createdAt])\n  @@index([role, status])\n  @@map(\"users\")\n}\n\n// 产品表（基础信息）\nmodel Product {\n  id          Int           @id @default(autoincrement())\n  name        String        @unique\n  description String?\n  category    String?\n  status      ProductStatus @default(ACTIVE)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // 关系\n  versions ProductVersion[]\n\n  @@index([status])\n  @@index([category])\n  @@index([createdAt])\n  @@map(\"products\")\n}\n\n// 产品版本表（包含默认验证模板和加密密钥）\nmodel ProductVersion {\n  id             Int           @id @default(autoincrement())\n  productId      Int\n  version        String\n  versionName    String?\n  description    String?\n  configTemplate String // JSON格式：字典数组\n  encryptionKey  String\n  defaultPrice   Float\n  downloadLink   String?\n  coverUrl       String?\n  changelog      String?\n  status         ProductStatus @default(ACTIVE)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // 关系\n  product        Product         @relation(fields: [productId], references: [id], onDelete: Cascade)\n  licenses       License[]\n  authorizations Authorization[]\n\n  @@unique([productId, version])\n  @@index([productId])\n  @@index([status])\n  @@index([createdAt])\n  @@index([productId, status])\n  @@map(\"product_versions\")\n}\n\n// 分发商授权表（店铺商品授权）\nmodel Authorization {\n  id            Int                 @id @default(autoincrement())\n  distributorId Int\n  versionId     Int\n  customPrice   Float?\n  status        AuthorizationStatus @default(ACTIVE)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // 关系\n  distributor User           @relation(fields: [distributorId], references: [id], onDelete: Cascade)\n  version     ProductVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)\n\n  @@unique([distributorId, versionId])\n  @@index([distributorId])\n  @@index([versionId])\n  @@index([status])\n  @@index([createdAt])\n  @@index([distributorId, status])\n  @@map(\"authorizations\")\n}\n\n// 许可证表\nmodel License {\n  id            Int           @id @default(autoincrement())\n  versionId     Int\n  licenseKey    String        @unique\n  status        LicenseStatus @default(INACTIVE)\n  verifyConfig  String? // JSON格式：字典，\n  activatedAt   DateTime?\n  distributorId Int\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // 关系\n  version     ProductVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)\n  distributor User           @relation(fields: [distributorId], references: [id], onDelete: Restrict)\n\n  @@index([licenseKey])\n  @@index([versionId])\n  @@index([distributorId])\n  @@index([status])\n  @@index([activatedAt])\n  @@index([createdAt])\n  @@index([distributorId, status])\n  @@index([status, createdAt])\n  @@map(\"licenses\")\n}\n\n// 枚举类型定义\nenum UserRole {\n  ADMIN\n  DISTRIBUTOR\n}\n\nenum UserStatus {\n  ACTIVE\n  INACTIVE\n}\n\nenum ProductStatus {\n  ACTIVE\n  INACTIVE\n}\n\nenum AuthorizationStatus {\n  ACTIVE\n  INACTIVE\n}\n\nenum LicenseStatus {\n  INACTIVE\n  ACTIVE\n  REVOKED\n}\n",
  "inlineSchemaHash": "56640c81afd7d4c49cf10ab0970eff89e54030ad1578385a224918a85daaac60",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"username\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"passwordHash\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"role\",\"kind\":\"enum\",\"type\":\"UserRole\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"UserStatus\"},{\"name\":\"nickName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"wechat\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"avatar\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"licenses\",\"kind\":\"object\",\"type\":\"License\",\"relationName\":\"LicenseToUser\"},{\"name\":\"authorizations\",\"kind\":\"object\",\"type\":\"Authorization\",\"relationName\":\"AuthorizationToUser\"}],\"dbName\":\"users\"},\"Product\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"category\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"ProductStatus\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"versions\",\"kind\":\"object\",\"type\":\"ProductVersion\",\"relationName\":\"ProductToProductVersion\"}],\"dbName\":\"products\"},\"ProductVersion\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"productId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"version\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"versionName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"configTemplate\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"encryptionKey\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"defaultPrice\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"downloadLink\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"coverUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"changelog\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"ProductStatus\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"product\",\"kind\":\"object\",\"type\":\"Product\",\"relationName\":\"ProductToProductVersion\"},{\"name\":\"licenses\",\"kind\":\"object\",\"type\":\"License\",\"relationName\":\"LicenseToProductVersion\"},{\"name\":\"authorizations\",\"kind\":\"object\",\"type\":\"Authorization\",\"relationName\":\"AuthorizationToProductVersion\"}],\"dbName\":\"product_versions\"},\"Authorization\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"distributorId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"versionId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"customPrice\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"AuthorizationStatus\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"distributor\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"AuthorizationToUser\"},{\"name\":\"version\",\"kind\":\"object\",\"type\":\"ProductVersion\",\"relationName\":\"AuthorizationToProductVersion\"}],\"dbName\":\"authorizations\"},\"License\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"versionId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"licenseKey\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"LicenseStatus\"},{\"name\":\"verifyConfig\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"activatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"distributorId\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"version\",\"kind\":\"object\",\"type\":\"ProductVersion\",\"relationName\":\"LicenseToProductVersion\"},{\"name\":\"distributor\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"LicenseToUser\"}],\"dbName\":\"licenses\"}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: async () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine
  }
}
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

